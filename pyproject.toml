[project]
name = "app"
version = "0.1.0"
description = ""
readme = "README.md"
requires-python = ">=3.11,<3.14"
dependencies = [ "python-dotenv>=1.0.0,<2.0.0", "pydantic<2.10", "aiostream>=0.5.2,<0.6.0", "llama-index-core>=0.12.28,<0.13.0", "llama-index-server>=0.1.17,<0.2.0", "docx2txt>=0.8,<0.9", "llama-index-llms-openai>=0.3.2,<0.4.0", "llama-index-embeddings-openai>=0.3.1,<0.4.0" ]

[[project.authors]]
name = "<PERSON>"
email = "<EMAIL>"

[project.optional-dependencies]
dev = [ "mypy>=1.8.0,<2.0.0", "pytest>=8.3.5,<9.0.0", "pytest-asyncio>=0.25.3,<0.26.0" ]

[project.scripts]
generate = "generate:generate_index"
generate_index = "generate:generate_index"
generate_ui = "generate:generate_ui_for_workflow"

[tool]
[tool.mypy]
python_version = "3.11"
plugins = "pydantic.mypy"
exclude = [ "tests", "venv", ".venv", "output", "config" ]
check_untyped_defs = true
warn_unused_ignores = false
show_error_codes = true
namespace_packages = true
ignore_missing_imports = true
follow_imports = "silent"
implicit_optional = true
strict_optional = false
disable_error_code = [ "return-value", "assignment" ]

[[tool.mypy.overrides]]
module = "app.*"
ignore_missing_imports = false

[tool.hatch]
[tool.hatch.metadata]
allow-direct-references = true

[build-system]
requires = [ "hatchling>=1.24" ]
build-backend = "hatchling.build"